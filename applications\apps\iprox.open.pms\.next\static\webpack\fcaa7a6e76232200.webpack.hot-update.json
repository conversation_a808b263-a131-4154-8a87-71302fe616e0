{"c": ["app/layout", "app/[locale]/(authenticated)/(user)/dossier/[id]/page", "webpack"], "r": [], "m": ["(app-pages-browser)/../../node_modules/ky/distribution/core/Ky.js", "(app-pages-browser)/../../node_modules/ky/distribution/core/constants.js", "(app-pages-browser)/../../node_modules/ky/distribution/errors/TimeoutError.js", "(app-pages-browser)/../../node_modules/ky/distribution/index.js", "(app-pages-browser)/../../node_modules/ky/distribution/utils/delay.js", "(app-pages-browser)/../../node_modules/ky/distribution/utils/is.js", "(app-pages-browser)/../../node_modules/ky/distribution/utils/merge.js", "(app-pages-browser)/../../node_modules/ky/distribution/utils/normalize.js", "(app-pages-browser)/../../node_modules/ky/distribution/utils/options.js", "(app-pages-browser)/../../node_modules/ky/distribution/utils/timeout.js", "(app-pages-browser)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProducten%5C%5Ciprox-open%5C%5Cprojects%5C%5Ciprox.open%5C%5Capplications%5C%5Capps%5C%5Ciprox.open.pms%5C%5Capp%5C%5C%5Blocale%5D%5C%5C(authenticated)%5C%5C(user)%5C%5Cdossier%5C%5C%5Bid%5D%5C%5Cpage-content.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProducten%5C%5Ciprox-open%5C%5Cprojects%5C%5Ciprox.open%5C%5Capplications%5C%5Clibs%5C%5Creact-ui%5C%5Csrc%5C%5Cindex.ts%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProducten%5C%5Ciprox-open%5C%5Cprojects%5C%5Ciprox.open%5C%5Capplications%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!", "(app-pages-browser)/./app/[locale]/(authenticated)/(user)/dossier/[id]/page-content.tsx", "(app-pages-browser)/./app/_components/add-button-modal-content.tsx", "(app-pages-browser)/./app/_components/blocked-files-list.tsx", "(app-pages-browser)/./app/_components/create-folder-form.tsx", "(app-pages-browser)/./app/_components/dossier-file-manager.tsx", "(app-pages-browser)/./app/_components/dossier-image-field.tsx", "(app-pages-browser)/./app/_components/dossier-status-controls.tsx", "(app-pages-browser)/./app/_components/dossier-title.tsx", "(app-pages-browser)/./app/_components/file-upload-item.tsx", "(app-pages-browser)/./app/_components/file-uploader.tsx", "(app-pages-browser)/./app/_components/styles/file-uploader.module.scss", "(app-pages-browser)/./app/_config/allowed-file-types.ts", "(app-pages-browser)/./app/_http/fetcher-api.client.ts", "(app-pages-browser)/./app/_services/dossier-service.client.ts", "(app-pages-browser)/./app/_utils/array.ts", "(app-pages-browser)/./app/_utils/dossier.form-changed.ts", "(app-pages-browser)/./app/_utils/dossier.form-definition.hook.ts", "(app-pages-browser)/./app/_utils/get-validation-rules-mapping.ts", "(app-pages-browser)/./app/_utils/save-file.ts", "(app-pages-browser)/./app/_utils/use-dynamic-field-mapper.hook.ts", "(app-pages-browser)/./app/_utils/use-title-definition.ts"]}