/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(authenticated)/(user)/dossier/[id]/page",{

/***/ "(app-pages-browser)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProducten%5C%5Ciprox-open%5C%5Cprojects%5C%5Ciprox.open%5C%5Capplications%5C%5Clibs%5C%5Creact-ui%5C%5Csrc%5C%5Cindex.ts%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProducten%5C%5Ciprox-open%5C%5Cprojects%5C%5Ciprox.open%5C%5Capplications%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProducten%5C%5Ciprox-open%5C%5Cprojects%5C%5Ciprox.open%5C%5Capplications%5C%5Clibs%5C%5Creact-ui%5C%5Csrc%5C%5Cindex.ts%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProducten%5C%5Ciprox-open%5C%5Cprojects%5C%5Ciprox.open%5C%5Capplications%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ../../libs/react-ui/src/index.ts */ \"(app-pages-browser)/../../libs/react-ui/src/index.ts\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ../../node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js */ \"(app-pages-browser)/../../node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNQcm9kdWN0ZW4lNUMlNUNpcHJveC1vcGVuJTVDJTVDcHJvamVjdHMlNUMlNUNpcHJveC5vcGVuJTVDJTVDYXBwbGljYXRpb25zJTVDJTVDbGlicyU1QyU1Q3JlYWN0LXVpJTVDJTVDc3JjJTVDJTVDaW5kZXgudHMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjIqJTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNQcm9kdWN0ZW4lNUMlNUNpcHJveC1vcGVuJTVDJTVDcHJvamVjdHMlNUMlNUNpcHJveC5vcGVuJTVDJTVDYXBwbGljYXRpb25zJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dC1pbnRsJTVDJTVDZGlzdCU1QyU1Q2VzbSU1QyU1Q3NoYXJlZCU1QyU1Q05leHRJbnRsQ2xpZW50UHJvdmlkZXIuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJkZWZhdWx0JTIyJTVEJTdEJnNlcnZlcj1mYWxzZSEiLCJtYXBwaW5ncyI6IkFBQUEsMExBQWlJO0FBQ2pJO0FBQ0Esc1FBQXNNIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8/NTA4ZCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXFByb2R1Y3RlblxcXFxpcHJveC1vcGVuXFxcXHByb2plY3RzXFxcXGlwcm94Lm9wZW5cXFxcYXBwbGljYXRpb25zXFxcXGxpYnNcXFxccmVhY3QtdWlcXFxcc3JjXFxcXGluZGV4LnRzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiRDpcXFxcUHJvZHVjdGVuXFxcXGlwcm94LW9wZW5cXFxccHJvamVjdHNcXFxcaXByb3gub3BlblxcXFxhcHBsaWNhdGlvbnNcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHQtaW50bFxcXFxkaXN0XFxcXGVzbVxcXFxzaGFyZWRcXFxcTmV4dEludGxDbGllbnRQcm92aWRlci5qc1wiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProducten%5C%5Ciprox-open%5C%5Cprojects%5C%5Ciprox.open%5C%5Capplications%5C%5Clibs%5C%5Creact-ui%5C%5Csrc%5C%5Cindex.ts%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProducten%5C%5Ciprox-open%5C%5Cprojects%5C%5Ciprox.open%5C%5Capplications%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!\n"));

/***/ })

});